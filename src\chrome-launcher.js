// Chrome启动器核心逻辑
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');
const Config = require('./config');

class ChromeLauncher {
    constructor() {
        this.chromePath = Config.DEFAULT_CHROME_PATH;
        this.headlessMode = false;
        this.minimizeMode = false;
        this.headlessProcesses = [];
        this.logCallback = null;
        this.activeWorkers = 0;
        this.maxWorkers = Config.MAX_BING_WORKERS;
    }

    setLogCallback(callback) {
        this.logCallback = callback;
    }

    log(message, type = 'info') {
        if (this.logCallback) {
            this.logCallback(message, type);
        }
    }

    // 检测Chrome配置文件
    async detectChromeProfiles() {
        try {
            const userDataPaths = Config.getChromeUserDataPaths();
            let profiles = [];

            for (const userDataPath of userDataPaths) {
                if (fs.existsSync(userDataPath)) {
                    // 检查Default配置文件
                    const defaultPath = path.join(userDataPath, 'Default');
                    if (fs.existsSync(defaultPath)) {
                        profiles.push('Default');
                    }

                    // 检查Profile X配置文件
                    try {
                        const items = fs.readdirSync(userDataPath);
                        for (const item of items) {
                            const itemPath = path.join(userDataPath, item);
                            if (fs.statSync(itemPath).isDirectory() && item.startsWith('Profile ')) {
                                const preferencesFile = path.join(itemPath, 'Preferences');
                                if (fs.existsSync(preferencesFile)) {
                                    profiles.push(item);
                                }
                            }
                        }
                    } catch (error) {
                        // 忽略权限错误
                    }

                    // 如果找到了配置文件，就使用第一个找到的Chrome版本
                    if (profiles.length > 0) {
                        break;
                    }
                }
            }

            // 去重并自然排序
            profiles = [...new Set(profiles)];
            profiles.sort(this.naturalSortKey);

            return profiles.length > 0 ? profiles : ['Default'];
        } catch (error) {
            return ['Default'];
        }
    }

    naturalSortKey(profile) {
        if (profile === 'Default') {
            return [0, 0];
        } else if (profile.startsWith('Profile ')) {
            try {
                const num = parseInt(profile.split(' ')[1]);
                return [1, num];
            } catch (error) {
                return [2, profile];
            }
        } else {
            return [2, profile];
        }
    }

    // 启动Chrome实例
    async launchChromeInstance(profileName, url = null) {
        try {
            if (!fs.existsSync(this.chromePath)) {
                throw new Error(`Chrome路径不存在: ${this.chromePath}`);
            }

            const displayProfile = Config.getProfileDisplayName(profileName);
            const urlDisplayName = url ? Config.getUrlDisplayName(url) : '浏览器';

            this.log(`启动: 配置文件 ${displayProfile} -> ${urlDisplayName}`);

            if (this.headlessMode) {
                return await this.launchHeadlessChrome(profileName, url);
            } else {
                return await this.launchNormalChrome(profileName, url);
            }
        } catch (error) {
            const displayProfile = Config.getProfileDisplayName(profileName);
            this.log(`✗ 启动Chrome失败 (配置文件 ${displayProfile}): ${error.message}`, 'error');

            // 提供更详细的错误信息和解决建议
            if (error.code === 'ENOENT') {
                this.log('错误详情: Chrome浏览器未找到，请检查安装路径', 'error');
                this.log('解决方案: 请确保Chrome浏览器已正确安装', 'warning');
            } else if (error.code === 'EACCES') {
                this.log('错误详情: 权限不足', 'error');
                this.log('解决方案: 请以管理员身份运行程序', 'warning');
            } else if (error.message.includes('Chrome路径不存在')) {
                this.log('解决方案: 请检查Chrome安装路径配置', 'warning');
            }

            throw error;
        }
    }

    // 启动无头模式Chrome
    async launchHeadlessChrome(profileName, url) {
        try {
            const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'chrome_headless_'));
            const downloadDir = Config.getUserStalkDir();

            // 确保下载目录存在
            if (!fs.existsSync(downloadDir)) {
                fs.mkdirSync(downloadDir, { recursive: true });
            }

            const args = Config.getChromeHeadlessArgs(tempDir, downloadDir);
            if (url) {
                args.push(url);
            }

            const process = spawn(this.chromePath, args, {
                detached: false,
                stdio: 'ignore'
            });

            // 监听进程错误
            process.on('error', (error) => {
                this.log(`✗ 无头模式Chrome进程启动失败: ${error.message}`, 'error');
                // 清理临时目录
                if (fs.existsSync(tempDir)) {
                    try {
                        fs.rmSync(tempDir, { recursive: true, force: true });
                    } catch (cleanupError) {
                        this.log(`警告: 清理临时目录失败: ${cleanupError.message}`, 'warning');
                    }
                }
            });

            this.headlessProcesses.push({
                process: process,
                tempDir: tempDir,
                profileName: profileName
            });

            return process;
        } catch (error) {
            this.log(`✗ 无头模式Chrome启动失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 启动普通模式Chrome
    async launchNormalChrome(profileName, url) {
        try {
            const args = [`--profile-directory=${profileName}`];

            // 如果启用了最小化模式，添加最小化参数
            if (this.minimizeMode) {
                args.push('--start-minimized');
            }

            if (url) {
                args.push(url);
            }

            const process = spawn(this.chromePath, args, {
                detached: true,
                stdio: 'ignore'
            });

            // 监听进程错误
            process.on('error', (error) => {
                this.log(`✗ 普通模式Chrome进程启动失败: ${error.message}`, 'error');
            });

            process.unref();
            return process;
        } catch (error) {
            this.log(`✗ 普通模式Chrome启动失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 按顺序访问URL列表
    async visitUrlsSequence(profileName, urls, closeAfter = true) {
        try {
            const displayProfile = Config.getProfileDisplayName(profileName);

            if (!this.headlessMode) {
                // 普通模式：先启动浏览器
                await this.launchChromeInstance(profileName);
                await this.delay(Config.BROWSER_START_DELAY);
            }

            // 访问每个URL
            for (let i = 0; i < urls.length; i++) {
                const url = urls[i];
                const displayName = Config.getUrlDisplayName(url);
                this.log(`  访问 ${displayName}`);

                if (this.headlessMode) {
                    // 无头模式：为每个URL启动独立实例
                    await this.launchChromeInstance(profileName, url);
                } else {
                    // 普通模式：在现有浏览器中打开新标签
                    spawn(this.chromePath, [`--profile-directory=${profileName}`, url], {
                        detached: true,
                        stdio: 'ignore'
                    });
                }

                await this.delay(Config.PAGE_LOAD_DELAY);
            }

            // 等待处理完成
            if (closeAfter) {
                await this.delay(Config.PROCESS_END_DELAY);
                this.log(`✓ 配置文件 ${displayProfile} 的流程已完成`);
            }

        } catch (error) {
            const displayProfile = Config.getProfileDisplayName(profileName);
            this.log(`✗ 配置文件 ${displayProfile} 的URL访问流程失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 启动配置文件并打开网站
    async launchProfileWithWebsite(profileName, url) {
        try {
            const displayUrl = Config.getUrlDisplayName(url);
            const displayProfile = Config.getProfileDisplayName(profileName);
            this.log(`启动配置文件 ${displayProfile} 并打开 ${displayUrl}`);

            await this.launchChromeInstance(profileName, url);
            this.log(`✓ 配置文件 ${displayProfile} 启动成功并打开网站`);

        } catch (error) {
            const displayProfile = Config.getProfileDisplayName(profileName);
            this.log(`✗ 启动配置文件 ${displayProfile} 并打开网站失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 关闭所有Chrome进程
    async closeAllChrome() {
        try {
            this.log('尝试关闭所有Chrome进程...');

            const { exec } = require('child_process');
            return new Promise((resolve) => {
                exec('taskkill /f /im chrome.exe', (error, stdout, stderr) => {
                    if (error) {
                        if (error.code === 128) {
                            this.log('⚠ 没有找到Chrome进程');
                        } else {
                            this.log(`⚠ 关闭Chrome进程时出现问题: ${error.message}`, 'warning');
                        }
                    } else {
                        this.log('✓ 所有Chrome进程已关闭');
                    }
                    resolve();
                });
            });
        } catch (error) {
            this.log(`✗ 关闭Chrome进程失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 清理无头模式进程
    async closeHeadlessProcesses() {
        try {
            if (this.headlessMode && this.headlessProcesses.length > 0) {
                this.log('🔒 无头模式：正在清理浏览器进程和临时文件...');

                let cleanedCount = 0;
                for (const processInfo of this.headlessProcesses) {
                    try {
                        const { process, tempDir } = processInfo;

                        // 终止进程
                        if (!process.killed) {
                            process.kill('SIGTERM');
                            
                            // 等待进程结束，如果5秒后还没结束就强制杀死
                            setTimeout(() => {
                                if (!process.killed) {
                                    process.kill('SIGKILL');
                                }
                            }, 5000);
                        }

                        // 清理临时目录
                        if (tempDir && fs.existsSync(tempDir)) {
                            try {
                                fs.rmSync(tempDir, { recursive: true, force: true });
                            } catch (error) {
                                // 忽略清理错误
                            }
                        }

                        cleanedCount++;
                    } catch (error) {
                        this.log(`⚠ 清理单个进程时出错: ${error.message}`, 'warning');
                    }
                }

                // 清空进程列表
                this.headlessProcesses = [];

                this.log(`✓ 已清理 ${cleanedCount} 个无头模式浏览器实例`);
                this.log('🎯 无头模式清理完毕，用户Chrome不受影响');
            } else {
                this.log('ℹ 非无头模式或无进程需要清理');
            }
        } catch (error) {
            this.log(`⚠ 清理无头模式浏览器时出现问题: ${error.message}`, 'warning');
        }
    }

    // 延迟函数
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 设置Chrome路径
    setChromePath(path) {
        this.chromePath = path;
    }

    // 设置无头模式
    setHeadlessMode(enabled) {
        this.headlessMode = enabled;
    }

    // 设置最小化模式
    setMinimizeMode(enabled) {
        this.minimizeMode = enabled;
    }

    // 创建今日文件夹
    async createTodayFolder() {
        try {
            const todayPath = Config.getTodayFolderPath();
            const stalkDir = Config.getUserStalkDir();

            // 确保Stalk目录存在
            if (!fs.existsSync(stalkDir)) {
                fs.mkdirSync(stalkDir, { recursive: true });
                this.log(`✓ 创建Stalk目录: ${stalkDir}`);
            }

            // 创建今日文件夹
            if (!fs.existsSync(todayPath)) {
                fs.mkdirSync(todayPath, { recursive: true });
                this.log(`✓ 创建今日文件夹: ${Config.getTodayFolderName()}`);
                return { success: true, message: `今日文件夹已创建: ${Config.getTodayFolderName()}` };
            } else {
                this.log(`ℹ 今日文件夹已存在: ${Config.getTodayFolderName()}`);
                return { success: true, message: `今日文件夹已存在: ${Config.getTodayFolderName()}` };
            }
        } catch (error) {
            this.log(`✗ 创建今日文件夹失败: ${error.message}`, 'error');
            return { success: false, message: `创建文件夹失败: ${error.message}` };
        }
    }

    // 清除今日缓存 - 完全对应Python版本逻辑
    async clearTodayCache() {
        try {
            const todayPath = Config.getTodayFolderPath();
            const todayFolderName = Config.getTodayFolderName();

            // 检查今日文件夹是否存在
            if (!fs.existsSync(todayPath)) {
                this.log(`ℹ 今日文件夹 ${todayFolderName} 不存在，无需清除缓存`);
                return { success: true, message: `今日文件夹 ${todayFolderName} 不存在，无需清除缓存` };
            }

            // 查找所有txt文件
            const files = fs.readdirSync(todayPath);
            const txtFiles = files.filter(file => file.toLowerCase().endsWith('.txt'));

            if (txtFiles.length === 0) {
                this.log(`ℹ 今日文件夹 ${todayFolderName} 中没有txt文件，无需清除`);
                return { success: true, message: `今日文件夹 ${todayFolderName} 中没有txt文件，无需清除` };
            }

            this.log(`🗑️ 开始清除今日文件夹 ${todayFolderName} 中的 ${txtFiles.length} 个txt文件...`);

            // 删除所有txt文件
            let deletedCount = 0;
            const failedFiles = [];

            for (const txtFile of txtFiles) {
                const filePath = path.join(todayPath, txtFile);
                try {
                    fs.unlinkSync(filePath);
                    deletedCount++;
                    this.log(`✓ 已删除: ${txtFile}`);
                } catch (error) {
                    failedFiles.push(txtFile);
                    this.log(`✗ 删除失败: ${txtFile} - ${error.message}`);
                }
            }

            // 显示结果
            if (failedFiles.length > 0) {
                const message = `清除缓存完成：成功删除 ${deletedCount} 个文件，${failedFiles.length} 个文件删除失败`;
                this.log(`⚠️ ${message}`);
                return { success: true, message: message };
            } else {
                const message = `清除缓存完成：成功删除 ${deletedCount} 个txt文件`;
                this.log(`✅ ${message}`);
                return { success: true, message: message };
            }

        } catch (error) {
            this.log(`✗ 清除缓存失败: ${error.message}`);
            return { success: false, message: `清除缓存失败: ${error.message}` };
        }
    }

    // 并发执行任务
    async executeWithConcurrency(profiles, taskFunction, maxWorkers = null) {
        if (maxWorkers) {
            this.maxWorkers = maxWorkers;
        }

        const results = [];
        const executing = [];

        for (const profile of profiles) {
            const task = taskFunction(profile).then(result => {
                this.activeWorkers--;
                return result;
            });

            results.push(task);
            executing.push(task);
            this.activeWorkers++;

            if (this.activeWorkers >= this.maxWorkers) {
                await Promise.race(executing);
                executing.splice(executing.findIndex(p => p === task), 1);
            }
        }

        return Promise.all(results);
    }

    // 设置最大并发数
    setMaxWorkers(maxWorkers) {
        this.maxWorkers = maxWorkers;
    }

    // 执行哔哩搜索流程 - 完全对应Python版本（两次搜索）
    async executeBilibiliSearchFlow(profileName) {
        try {
            const displayProfile = Config.getProfileDisplayName(profileName);

            // 使用固定的哔哩搜索URL
            const bilibiliUrl = "https://www.bing.com/search?q=%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9&qs=n&form=BILREW";

            // 第一次访问哔哩搜索
            this.log(`  第一次访问哔哩搜索`, 'bilibili');
            await this.launchChromeInstance(profileName, bilibiliUrl);

            // 等待2秒后进行第二次访问
            await this.delay(2000);

            // 第二次访问哔哩搜索
            this.log(`  第二次访问哔哩搜索`, 'bilibili');
            await this.launchChromeInstance(profileName, bilibiliUrl);

            this.log(`✓ 配置文件 ${displayProfile} 的哔哩搜索流程已完成`, 'bilibili');

        } catch (error) {
            const displayProfile = Config.getProfileDisplayName(profileName);
            this.log(`✗ 配置文件 ${displayProfile} 的哔哩搜索流程失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 执行自动模式的完整7步流程 - 完全对应Python版本
    async executeAutoModeFlow(profileName) {
        try {
            const displayProfile = Config.getProfileDisplayName(profileName);
            this.log(`开始: 配置文件 ${displayProfile} 开始自动模式流程...`, 'auto');

            // 定义URL
            const bingRewardsUrl = "https://rewards.bing.com/?form=BILREW";
            const bilibiliSearchUrl = "https://www.bing.com/search?q=%E5%93%94%E5%93%A9%E5%93%94%E5%93%A9&qs=n&form=BILREW";

            // 按照指定顺序访问URL（7步流程）
            // 1. 访问一次必应奖励
            this.log(`  第1步: 访问必应奖励`, 'bing');
            await this.launchChromeInstance(profileName, bingRewardsUrl);
            await this.delay(Config.PAGE_LOAD_DELAY);

            // 2. 访问两次哔哩搜索
            this.log(`  第2步: 第一次访问哔哩搜索`, 'bilibili');
            await this.launchChromeInstance(profileName, bilibiliSearchUrl);
            await this.delay(Config.PAGE_LOAD_DELAY);

            this.log(`  第3步: 第二次访问哔哩搜索`, 'bilibili');
            await this.launchChromeInstance(profileName, bilibiliSearchUrl);
            await this.delay(Config.PAGE_LOAD_DELAY);

            // 3. 访问一次必应奖励
            this.log(`  第4步: 访问必应奖励`, 'bing');
            await this.launchChromeInstance(profileName, bingRewardsUrl);
            await this.delay(Config.PAGE_LOAD_DELAY);

            // 4. 访问两次哔哩搜索
            this.log(`  第5步: 第三次访问哔哩搜索`, 'bilibili');
            await this.launchChromeInstance(profileName, bilibiliSearchUrl);
            await this.delay(Config.PAGE_LOAD_DELAY);

            this.log(`  第6步: 第四次访问哔哩搜索`, 'bilibili');
            await this.launchChromeInstance(profileName, bilibiliSearchUrl);
            await this.delay(Config.PAGE_LOAD_DELAY);

            // 5. 访问一次必应奖励
            this.log(`  第7步: 访问必应奖励`, 'bing');
            await this.launchChromeInstance(profileName, bingRewardsUrl);
            await this.delay(Config.PAGE_LOAD_DELAY);

            // 等待处理完成
            await this.delay(Config.PROCESS_END_DELAY);
            this.log(`✓ 配置文件 ${displayProfile} 的自动模式流程已完成`, 'auto');

        } catch (error) {
            const displayProfile = Config.getProfileDisplayName(profileName);
            this.log(`✗ 配置文件 ${displayProfile} 的自动模式流程失败: ${error.message}`, 'error');
            throw error;
        }
    }
}

module.exports = ChromeLauncher;
