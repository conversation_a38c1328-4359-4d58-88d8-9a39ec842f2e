<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>CPL</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css?v=20250128">
</head>
<body>
    <!-- 顶部分割线 -->
    <div class="top-divider"></div>
    <div class="app">
        <!-- 左右两栏布局 -->
        <div class="main-layout">
            <!-- 左侧日志区域 -->
            <div class="left-panel">
                <div class="log-section">
                    <div class="section-header">
                        <span class="section-title">
                            <i class="fas fa-terminal"></i>
                        </span>
                        <div class="header-actions">
                            <button class="icon-btn" onclick="copyLog()" title="复制日志">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="icon-btn" onclick="clearLog()" title="清空日志">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                            <button class="icon-btn" onclick="saveLog()" title="保存日志">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    <div class="log-container">
                        <div class="log-content" id="logContent">
                            <!-- 日志内容将动态加载 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧功能区域 -->
            <div class="right-panel">
                <!-- 配置文件区域 -->
                <div class="profiles-section">
                    <div class="section-header">
                        <span class="section-title">
                            <i class="fas fa-user-cog"></i>
                        </span>
                        <div class="header-actions">
                            <button class="icon-btn" onclick="selectAll()" title="全选">
                                <i class="fas fa-check-square"></i>
                            </button>
                            <button class="icon-btn" onclick="deselectAll()" title="清空选择">
                                <i class="far fa-square"></i>
                            </button>
                            <button class="icon-btn" onclick="refreshProfiles()" title="刷新配置">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="profiles-container">
                        <div class="profiles-grid" id="profileGrid">
                            <!-- 配置文件项将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 批量操作区域 -->
                <div class="batch-section">
                    <div class="section-header">
                        <span class="section-title">
                            <i class="fas fa-play-circle"></i>
                        </span>
                        <span class="selection-count" id="selectionCount">已选择: 0</span>
                    </div>
                    <div class="batch-container">
                        <!-- 第一行 -->
                        <div class="button-row">
                            <button class="batch-btn primary" onclick="launchBingRewards()">必应奖励</button>
                            <button class="batch-btn bilibili" onclick="launchBilibiliSearch()">哔哩搜索</button>
                        </div>
                        <!-- 第二行 -->
                        <div class="button-row">
                            <button class="batch-btn info" onclick="autoMode()">自动模式</button>
                            <button class="batch-btn secondary" onclick="launchAccountSignup()">账号注册</button>
                        </div>
                        <!-- 第三行 -->
                        <div class="button-row">
                            <button class="batch-btn warning" onclick="clearTodayCache()">清除缓存</button>
                            <button class="batch-btn success" onclick="readRecords()">读取记录</button>
                        </div>
                        <!-- 第四行 -->
                        <div class="button-row">
                            <button class="batch-btn option" id="headlessBtn" onclick="toggleHeadlessMode()">Headless</button>
                            <button class="batch-btn option" id="minimizeBtn" onclick="toggleMinimizeMode()">Minimize</button>
                        </div>
                        <!-- 第五行 -->
                        <div class="button-row">
                            <button class="batch-btn danger" onclick="closeAllChrome()">关闭所有</button>
                            <button class="batch-btn placeholder" disabled>占位符</button>
                        </div>
                    </div>
                </div>

                <!-- 扩展区域（用于显示结果等） -->
                <div class="extension-section">
                    <!-- 可扩展内容区域 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 结果面板 -->
    <div class="modal-overlay" id="resultsPanel" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>签到记录</h3>
                <button class="icon-btn" onclick="closeResults()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="resultsContent">
                <!-- 结果内容将动态加载 -->
            </div>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">正在处理...</div>
        </div>
    </div>

    <script src="../src/renderer.js"></script>
</body>
</html>
