import os
import time
import json
import random
import string
import secrets
from faker import Faker
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def generate_strong_password(length=16):
    """生成强密码"""
    chars = string.ascii_letters + string.digits + "!@#$%^&*"
    
    while True:
        password = ''.join(secrets.choice(chars) for _ in range(length))
        
        if (any(c.islower() for c in password) 
                and any(c.isupper() for c in password)
                and any(c.isdigit() for c in password)
                and any(c in "!@#$%^&*" for c in password)):
            return password

def random_email(length):
    """生成随机邮箱名"""
    first_char = random.choice(string.ascii_lowercase)
    
    other_chars = []
    for _ in range(length - 1):  
        if random.random() < 0.07:  
            other_chars.append(random.choice(string.digits))
        else: 
            other_chars.append(random.choice(string.ascii_lowercase))
    
    return first_char + ''.join(other_chars)

def open_browser(browser_path=""):
    """打开浏览器"""
    try:
        # 配置Chrome选项
        options = uc.ChromeOptions()
        
        # 添加基本反检测选项
        options.add_argument('--no-first-run')
        options.add_argument('--no-default-browser-check')
        options.add_argument('--disable-blink-features=AutomationControlled')
        
        # 设置用户代理
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # 如果指定了浏览器路径，使用自定义浏览器
        if browser_path and browser_path.strip() and os.path.exists(browser_path):
            options.binary_location = browser_path
            print(f"使用自定义浏览器: {browser_path}")
        else:
            print("使用默认Chrome浏览器")
        
        # 创建undetected-chromedriver实例
        driver = uc.Chrome(options=options, version_main=None)
        
        # 设置窗口大小
        driver.set_window_size(1280, 720)
        
        # 执行反检测脚本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return driver
        
    except Exception as e:
        print(f"浏览器启动失败: {e}")
        return None

def outlook_register(driver, email, password, bot_protection_wait, max_captcha_retries):
    """Outlook注册流程"""
    fake = Faker()
    
    lastname = fake.last_name()
    firstname = fake.first_name()
    year = str(random.randint(1960, 2005))
    month = str(random.randint(1, 12))
    day = str(random.randint(1, 28))
    
    try:
        print(f"正在访问Outlook注册页面...")
        driver.get("https://outlook.live.com/mail/0/?prompt=create_account")
        print("页面加载成功，等待同意按钮...")
        
        # 等待同意按钮出现并点击
        wait = WebDriverWait(driver, 30)
        agree_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//*[contains(text(), '同意并继续')]")))
        start_time = time.time()
        time.sleep(2)
        agree_button.click()
        print("已点击同意并继续")
        
    except Exception as e:
        print(f"[Error: 页面访问失败] - 无法访问Outlook注册页面: {e}")
        print("可能的原因:")
        print("1. 网络连接问题")
        print("2. IP被封禁")
        print("3. 页面结构发生变化")
        return False
    
    try:
        wait = WebDriverWait(driver, 10)
        
        # 输入邮箱地址
        email_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[aria-label="新建电子邮件"]')))
        for char in email:
            email_input.send_keys(char)
            time.sleep(0.08)  # 模拟打字延迟
        
        # 点击下一步
        next_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="primaryButton"]')))
        next_button.click()
        time.sleep(0.4)
        
        # 输入密码
        password_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[type="password"]')))
        for char in password:
            password_input.send_keys(char)
            time.sleep(0.06)  # 模拟打字延迟
        
        time.sleep(0.4)
        next_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="primaryButton"]')))
        next_button.click()
        
        time.sleep(0.5)
        # 填写出生年份
        year_input = wait.until(EC.presence_of_element_located((By.NAME, 'BirthYear')))
        year_input.clear()
        year_input.send_keys(year)
        
        try:
            time.sleep(0.6)
            # 尝试使用下拉选择月份
            month_select = Select(wait.until(EC.presence_of_element_located((By.NAME, 'BirthMonth'))))
            month_select.select_by_value(month)
            time.sleep(1.2)
            # 选择日期
            day_select = Select(driver.find_element(By.NAME, 'BirthDay'))
            day_select.select_by_value(day)
            
        except:
            # 如果下拉选择失败，尝试点击方式
            month_dropdown = driver.find_element(By.NAME, 'BirthMonth')
            month_dropdown.click()
            time.sleep(0.4)
            month_option = wait.until(EC.element_to_be_clickable((By.XPATH, f'//div[@role="option" and contains(text(), "{month}月")]')))
            month_option.click()
            time.sleep(1.2)
            
            day_dropdown = driver.find_element(By.NAME, 'BirthDay')
            day_dropdown.click()
            time.sleep(0.4)
            day_option = wait.until(EC.element_to_be_clickable((By.XPATH, f'//div[@role="option" and contains(text(), "{day}日")]')))
            day_option.click()
        
        next_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="primaryButton"]')))
        next_button.click()
        
        # 填写姓名
        lastname_input = wait.until(EC.presence_of_element_located((By.ID, 'lastNameInput')))
        for char in lastname:
            lastname_input.send_keys(char)
            time.sleep(0.12)  # 模拟打字延迟
        
        time.sleep(0.7)
        firstname_input = driver.find_element(By.ID, 'firstNameInput')
        firstname_input.clear()
        firstname_input.send_keys(firstname)
        
        # 等待机器人保护时间
        if time.time() - start_time < bot_protection_wait:
            time.sleep(bot_protection_wait - time.time() + start_time)
        
        next_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-testid="primaryButton"]')))
        next_button.click()
        
        # 等待页面加载
        wait_long = WebDriverWait(driver, 22)
        wait_long.until(EC.invisibility_of_element_located((By.CSS_SELECTOR, 'span > [href="https://go.microsoft.com/fwlink/?LinkID=521839"]')))
        
        time.sleep(0.4)
        
        # 检查是否有异常活动提示
        try:
            abnormal_activity = driver.find_elements(By.XPATH, "//*[contains(text(), '一些异常活动')]")
            if abnormal_activity:
                print("[Error: IP] - 当前IP注册频率过快。")
                return False
        except:
            pass
        
        # 检查是否有错误的验证码类型
        try:
            enforcement_frame = driver.find_elements(By.ID, 'enforcementFrame')
            if enforcement_frame:
                print("[Error: FunCaptcha] - 验证码类型错误，非按压验证码。")
                return False
        except:
            pass
        
        # 等待验证码加载
        try:
            wait_long = WebDriverWait(driver, 22)
            # 等待验证码iframe出现
            wait_long.until(lambda d: any("iframe.hsprotect.net" in iframe.get_attribute("src") or ""
                                        for iframe in d.find_elements(By.TAG_NAME, "iframe")
                                        if iframe.get_attribute("src")))
            time.sleep(0.8)
        except TimeoutException:
            print("[Error: Captcha] - 验证码加载超时")
            return False
        
        # 模拟Tab键导航到验证码
        actions = ActionChains(driver)
        actions.send_keys(Keys.TAB).perform()
        actions.send_keys(Keys.TAB).perform()
        time.sleep(0.1)
        
        # 尝试多次通过验证码
        for attempt in range(max_captcha_retries + 1):
            try:
                actions.send_keys(Keys.ENTER).perform()
                time.sleep(11)
                actions.send_keys(Keys.ENTER).perform()
                
                # 等待验证结果
                time.sleep(5)
                
                # 检查是否还有验证码
                try:
                    remaining_captcha = any("iframe.hsprotect.net" in iframe.get_attribute("src") or ""
                                          for iframe in driver.find_elements(By.TAG_NAME, "iframe")
                                          if iframe.get_attribute("src"))
                    if not remaining_captcha:
                        break
                except:
                    break
                    
            except Exception as e:
                print(f"验证码尝试 {attempt + 1} 失败: {e}")
                if attempt == max_captcha_retries:
                    raise TimeoutError("验证码尝试次数已达上限")
        
        # 检查是否通过验证但仍有频率限制
        try:
            time.sleep(1.2)
            abnormal_activity = driver.find_elements(By.XPATH, "//*[contains(text(), '一些异常活动')]")
            if abnormal_activity:
                print("[Error: Rate limit] - 正常通过验证码，但当前IP注册频率过快。")
                return False
        except:
            pass
        
        time.sleep(0.5)
        
    except Exception as e:
        print(f"[Error: Registration] - 注册过程失败: {e}")
        return False
    
    # 保存注册成功的邮箱
    os.makedirs('Results', exist_ok=True)
    filename = 'Results/registered_accounts.txt'
    with open(filename, 'a', encoding='utf-8') as f:
        f.write(f"{email}@outlook.com:{password}\n")
    print(f'[Success: Email Registration] - {email}@outlook.com:{password}')
    
    return True

def register_single_account(config):
    """注册单个账户"""
    driver = None
    try:
        driver = open_browser(config.get('browser_path', ''))
        if not driver:
            return False

        email = random_email(random.randint(12, 14))
        password = generate_strong_password(random.randint(11, 15))

        print(f"\n开始注册账户: {email}@outlook.com")
        result = outlook_register(
            driver,
            email,
            password,
            config['Bot_protection_wait'],
            config['max_captcha_retries']
        )

        return result

    except Exception as e:
        print(f"注册账户时发生异常: {e}")
        return False

    finally:
        if driver:
            try:
                driver.quit()
            except:
                pass

def main():
    """主函数"""
    print("=== 简化版 Outlook 注册工具 ===")
    print("特点: 单线程、无代理、基础功能")
    print()

    try:
        # 加载配置文件
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(script_dir, 'simple_config.json')

        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("✓ 配置文件加载成功")
    except Exception as e:
        print(f"✗ 配置文件加载失败: {e}")
        input("按回车键退出...")
        return

    # 创建结果目录
    os.makedirs("Results", exist_ok=True)

    print(f"配置信息:")
    print(f"  浏览器路径: {config.get('browser_path', '默认')}")
    print(f"  最大注册数量: {config['max_accounts']}")
    print(f"  机器人保护等待时间: {config['Bot_protection_wait']}秒")
    print(f"  验证码最大重试次数: {config['max_captcha_retries']}")
    print()

    # 测试浏览器启动
    print("正在测试浏览器启动...")
    try:
        driver = open_browser(config.get('browser_path', ''))
        if driver:
            print("✓ 浏览器启动成功")
            driver.quit()
        else:
            print("✗ 浏览器启动失败")
            input("按回车键退出...")
            return
    except Exception as e:
        print(f"✗ 浏览器启动异常: {e}")
        input("按回车键退出...")
        return

    print("\n开始执行注册任务...")

    success_count = 0
    fail_count = 0
    max_accounts = config['max_accounts']

    for i in range(max_accounts):
        print(f"\n--- 第 {i+1}/{max_accounts} 个账户 ---")

        try:
            result = register_single_account(config)
            if result:
                success_count += 1
                print(f"✓ 第 {i+1} 个账户注册成功")
            else:
                fail_count += 1
                print(f"✗ 第 {i+1} 个账户注册失败")

        except KeyboardInterrupt:
            print("\n用户中断程序")
            break
        except Exception as e:
            fail_count += 1
            print(f"✗ 第 {i+1} 个账户注册异常: {e}")

        # 在账户之间添加延迟
        if i < max_accounts - 1:
            print("等待 30 秒后继续下一个账户...")
            time.sleep(30)

    print(f"\n=== 注册完成 ===")
    print(f"总计: {max_accounts} 个")
    print(f"成功: {success_count} 个")
    print(f"失败: {fail_count} 个")
    print(f"成功率: {success_count/max_accounts*100:.1f}%")

    if success_count > 0:
        print(f"\n注册成功的账户已保存到: Results/registered_accounts.txt")

    print("\n程序执行完毕")
    input("按回车键退出...")

if __name__ == '__main__':
    main()
