const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class AutoRegister {
    constructor() {
        this.isRunning = false;
        this.currentProcess = null;
    }

    /**
     * 生成强密码
     */
    generateStrongPassword(length = 16) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
        let password = '';
        
        // 确保包含各种字符类型
        const requirements = [
            'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
            'abcdefghijklmnopqrstuvwxyz', 
            '0123456789',
            '!@#$%^&*'
        ];
        
        // 先从每个类型中选择一个字符
        for (const req of requirements) {
            password += req[Math.floor(Math.random() * req.length)];
        }
        
        // 填充剩余长度
        for (let i = password.length; i < length; i++) {
            password += chars[Math.floor(Math.random() * chars.length)];
        }
        
        // 打乱字符顺序
        return password.split('').sort(() => Math.random() - 0.5).join('');
    }

    /**
     * 生成随机邮箱名
     */
    generateRandomEmail(length = 13) {
        const letters = 'abcdefghijklmnopqrstuvwxyz';
        const digits = '0123456789';
        
        let email = letters[Math.floor(Math.random() * letters.length)]; // 首字符必须是字母
        
        for (let i = 1; i < length; i++) {
            if (Math.random() < 0.07) { // 7%概率添加数字
                email += digits[Math.floor(Math.random() * digits.length)];
            } else {
                email += letters[Math.floor(Math.random() * letters.length)];
            }
        }
        
        return email;
    }

    /**
     * 检查Python环境和依赖
     */
    async checkPythonEnvironment() {
        return new Promise((resolve) => {
            const pythonProcess = spawn('python', ['--version'], { shell: true });
            
            pythonProcess.on('close', (code) => {
                if (code === 0) {
                    resolve(true);
                } else {
                    resolve(false);
                }
            });
            
            pythonProcess.on('error', () => {
                resolve(false);
            });
        });
    }

    /**
     * 安装Python依赖
     */
    async installDependencies() {
        return new Promise((resolve, reject) => {
            const requirementsPath = path.join(__dirname, '..', 'RRR', 'requirements.txt');
            
            if (!fs.existsSync(requirementsPath)) {
                reject(new Error('requirements.txt文件不存在'));
                return;
            }

            const installProcess = spawn('pip', ['install', '-r', requirementsPath], { 
                shell: true,
                stdio: 'pipe'
            });

            let output = '';
            let errorOutput = '';

            installProcess.stdout.on('data', (data) => {
                output += data.toString();
            });

            installProcess.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });

            installProcess.on('close', (code) => {
                if (code === 0) {
                    resolve(output);
                } else {
                    reject(new Error(`依赖安装失败: ${errorOutput}`));
                }
            });
        });
    }

    /**
     * 创建自动注册配置文件
     */
    createAutoRegisterConfig(options = {}) {
        const config = {
            browser_path: options.browserPath || "",
            Bot_protection_wait: options.botProtectionWait || 60,
            max_captcha_retries: options.maxCaptchaRetries || 5,
            max_accounts: options.maxAccounts || 1,
            enable_oauth2: false,
            info: "Chrome Profile Launcher集成的自动注册配置"
        };

        const configPath = path.join(__dirname, '..', 'RRR', 'auto_register_config.json');
        fs.writeFileSync(configPath, JSON.stringify(config, null, 4), 'utf8');
        
        return configPath;
    }

    /**
     * 执行自动注册
     */
    async executeAutoRegister(options = {}) {
        if (this.isRunning) {
            throw new Error('自动注册正在运行中，请等待完成');
        }

        this.isRunning = true;

        try {
            // 检查Python环境
            const pythonAvailable = await this.checkPythonEnvironment();
            if (!pythonAvailable) {
                throw new Error('Python环境不可用，请确保已安装Python');
            }

            // 创建配置文件
            const configPath = this.createAutoRegisterConfig(options);

            // 执行注册脚本
            const result = await this.runRegistrationScript(configPath);
            
            return result;

        } finally {
            this.isRunning = false;
        }
    }

    /**
     * 运行注册脚本
     */
    async runRegistrationScript(configPath) {
        return new Promise((resolve, reject) => {
            // 使用独立的注册脚本
            const scriptPath = path.join(__dirname, 'outlook_register.py');

            if (!fs.existsSync(scriptPath)) {
                reject(new Error('注册脚本不存在'));
                return;
            }

            // 确保Results目录存在
            const resultsDir = path.join(__dirname, '..', 'Results');
            if (!fs.existsSync(resultsDir)) {
                fs.mkdirSync(resultsDir, { recursive: true });
            }

            // 执行Python脚本，传递配置文件和结果目录
            this.currentProcess = spawn('python', [scriptPath, configPath, resultsDir], {
                shell: true,
                stdio: 'pipe',
                cwd: __dirname
            });

            let output = '';
            let errorOutput = '';

            this.currentProcess.stdout.on('data', (data) => {
                const text = data.toString();
                output += text;
                console.log('注册输出:', text);
            });

            this.currentProcess.stderr.on('data', (data) => {
                const text = data.toString();
                errorOutput += text;
                console.error('注册错误:', text);
            });

            this.currentProcess.on('close', (code) => {
                // 清理临时文件
                try {
                    fs.unlinkSync(tempScriptPath);
                } catch (e) {
                    console.warn('清理临时文件失败:', e.message);
                }

                this.currentProcess = null;

                if (code === 0) {
                    resolve({
                        success: true,
                        output: output,
                        accounts: this.parseRegisteredAccounts()
                    });
                } else {
                    reject(new Error(`注册脚本执行失败 (退出码: ${code}): ${errorOutput}`));
                }
            });

            this.currentProcess.on('error', (error) => {
                reject(new Error(`注册脚本启动失败: ${error.message}`));
            });
        });
    }



    /**
     * 解析注册成功的账户
     */
    parseRegisteredAccounts() {
        // 使用程序根目录下的Results文件夹
        const resultsDir = path.join(__dirname, '..', 'Results');
        const accountsFile = path.join(resultsDir, 'registered_accounts.txt');

        if (!fs.existsSync(accountsFile)) {
            return [];
        }

        try {
            const content = fs.readFileSync(accountsFile, 'utf8');
            const lines = content.trim().split('\n').filter(line => line.trim());

            return lines.map(line => {
                const [email, password] = line.split(':');
                return { email, password };
            });
        } catch (error) {
            console.error('解析账户文件失败:', error);
            return [];
        }
    }

    /**
     * 保存注册成功的账户到程序目录
     */
    saveRegisteredAccount(email, password) {
        try {
            // 确保Results目录存在
            const resultsDir = path.join(__dirname, '..', 'Results');
            if (!fs.existsSync(resultsDir)) {
                fs.mkdirSync(resultsDir, { recursive: true });
            }

            // 保存账户信息
            const accountsFile = path.join(resultsDir, 'registered_accounts.txt');
            const accountInfo = `${email}:${password}\n`;

            fs.appendFileSync(accountsFile, accountInfo, 'utf8');

            // 同时保存到带时间戳的文件
            const timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
            const dailyFile = path.join(resultsDir, `accounts_${timestamp}.txt`);
            fs.appendFileSync(dailyFile, accountInfo, 'utf8');

            return true;
        } catch (error) {
            console.error('保存账户信息失败:', error);
            return false;
        }
    }

    /**
     * 停止当前注册进程
     */
    stopRegistration() {
        if (this.currentProcess) {
            this.currentProcess.kill('SIGTERM');
            this.currentProcess = null;
        }
        this.isRunning = false;
    }

    /**
     * 安装Python依赖
     */
    async installDependencies() {
        return new Promise((resolve, reject) => {
            const requirementsPath = path.join(__dirname, '..', 'requirements.txt');

            if (!fs.existsSync(requirementsPath)) {
                reject(new Error('requirements.txt文件不存在'));
                return;
            }

            const installProcess = spawn('pip', ['install', '-r', requirementsPath], {
                shell: true,
                stdio: 'pipe'
            });

            let output = '';
            let errorOutput = '';

            installProcess.stdout.on('data', (data) => {
                const text = data.toString();
                output += text;
                console.log('安装输出:', text);
            });

            installProcess.stderr.on('data', (data) => {
                const text = data.toString();
                errorOutput += text;
                console.error('安装错误:', text);
            });

            installProcess.on('close', (code) => {
                if (code === 0) {
                    resolve(output);
                } else {
                    reject(new Error(`依赖安装失败 (退出码: ${code}): ${errorOutput}`));
                }
            });

            installProcess.on('error', (error) => {
                reject(new Error(`启动pip安装失败: ${error.message}`));
            });
        });
    }

    /**
     * 获取运行状态
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            hasProcess: !!this.currentProcess
        };
    }
}

module.exports = AutoRegister;
