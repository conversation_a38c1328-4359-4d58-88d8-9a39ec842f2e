/* Chrome Profile Launcher - 重新设计 */

/* 全局重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 12px;
    line-height: 1.4;
    color: #333;
    background: #f8f9fa;
    overflow: hidden;
}

/* 顶部分割线 */
.top-divider {
    width: 100%;
    height: 1px;
    background: #e9ecef;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
}

/* 应用主容器 - 左右布局 */
.app {
    width: 100vw;
    height: calc(100vh - 1px);
    margin-top: 1px;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
}

/* 主布局容器 - 左右两栏 */
.main-layout {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: row;
    background: #f8f9fa;
}

/* 左侧面板 - 日志区域 - 50%宽度 */
.left-panel {
    width: 50%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    border-right: 1px solid #dee2e6;
}

/* 右侧面板 - 功能区域 - 50%宽度 */
.right-panel {
    width: 50%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
    overflow: hidden;
}

/* 配置文件区域 - 针对最小窗口优化 */
.profiles-section {
    background: #fff;
    display: flex;
    flex-direction: column;
    flex: 1.2;
    min-height: 120px;
    max-height: 35vh;
    border-bottom: 1px solid #dee2e6;
}

/* 批量操作区域 - 右侧中部，固定高度 */
.batch-section {
    background: #fff;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    height: auto;
    border-bottom: 1px solid #dee2e6;
}

/* 扩展区域 - 右侧下部，弹性空间 */
.extension-section {
    background: #fff;
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 100px;
}

/* 日志区域 - 左侧全高 */
.log-section {
    background: #fff;
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;
}

/* 统一的区域头部 - 700px宽度优化 */
.section-header {
    height: 30px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    flex-shrink: 0;
}

.section-title {
    font-size: 13px;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 6px;
}

.section-title i {
    font-size: 12px;
    color: #6c757d;
}

.selection-count {
    font-size: 12px;
    color: #6c757d;
}

/* 头部操作按钮 */
.header-actions {
    display: flex;
    gap: 4px;
}

/* 头部操作按钮 - 纯图标无边框 */
.icon-btn {
    width: 20px;
    height: 20px;
    border: none;
    background: transparent;
    color: #6c757d;
    cursor: pointer;
    border-radius: 0;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s ease;
}

/* 图标按钮悬停效果 - 纯图标样式 */
.icon-btn:hover {
    background: transparent;
    color: #007bff;
    transform: scale(1.1);
}

/* 配置文件容器 - 紧凑布局 */
.profiles-container {
    flex: 1;
    padding: 4px;
    overflow: hidden;
    min-height: 0;
    display: flex;
    flex-direction: column;
}

/* 配置文件网格 - 一行8个，紧凑布局 */
.profiles-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 2px;
    flex: 1;
    overflow-y: auto;
    align-content: start;
    padding: 4px;
}

/* 配置文件项 - 完全按照图片样式 */
.profile-item {
    aspect-ratio: 1;
    border: 1px solid #ced4da;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 13px;
    color: #495057;
    border-radius: 0;
    transition: none;
    user-select: none;
    font-weight: 500;
}

.profile-item:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
}

.profile-item.selected {
    background: #007bff;
    border-color: #007bff;
    color: #ffffff;
}

.profile-item.placeholder {
    background: transparent;
    border-color: #e9ecef;
    cursor: default;
}

.profile-item.placeholder:hover {
    background: transparent;
    border-color: #e9ecef;
}

/* 批量操作容器 - 紧凑布局 */
.batch-container {
    padding: 4px;
    flex-shrink: 0;
}

/* 按钮行 - 一行2个布局 */
.button-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3px;
    margin-bottom: 3px;
}

.button-row:last-child {
    margin-bottom: 0;
}

/* 批量操作按钮 - 文字按钮 */
.batch-btn {
    height: 32px;
    border: 1px solid;
    background: #fff;
    cursor: pointer;
    font-size: 12px;
    border-radius: 0;
    transition: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
}

/* 按钮颜色样式 - 文字按钮版本 */
.batch-btn.primary {
    border-color: #007bff;
    color: #007bff;
}

.batch-btn.primary:hover {
    background: #e7f3ff;
}

.batch-btn.success {
    border-color: #28a745;
    color: #28a745;
}

.batch-btn.success:hover {
    background: #e8f5e8;
}

.batch-btn.bilibili {
    border-color: #ff69b4;
    color: #ff69b4;
}

.batch-btn.bilibili:hover {
    background: #fff5fb;
}

.batch-btn.info {
    border-color: #17a2b8;
    color: #17a2b8;
}

.batch-btn.info:hover {
    background: #e6f7ff;
}

.batch-btn.secondary {
    border-color: #6c757d;
    color: #6c757d;
}

.batch-btn.secondary:hover {
    background: #f8f9fa;
}

.batch-btn.warning {
    border-color: #ffc107;
    color: #856404;
}

.batch-btn.warning:hover {
    background: #fff8e1;
}

.batch-btn.danger {
    border-color: #dc3545;
    color: #dc3545;
}

.batch-btn.danger:hover {
    background: #ffeaea;
}

.batch-btn.option {
    border-color: #6c757d;
    color: #6c757d;
}

.batch-btn.option:hover {
    background: #f8f9fa;
}

/* 选项按钮选中状态 - 紫色效果 */
.batch-btn.option.selected {
    background: #f3e8ff;
    border-color: #8b5cf6;
    color: #6b46c1;
}

.batch-btn.placeholder {
    border-color: #e9ecef;
    background: #fff;
    cursor: default;
    color: #adb5bd;
}



/* 日志系统 - 隐藏滚动条但保持滚动功能 */
.log-container {
    flex: 1;
    overflow: hidden;
}

.log-content {
    height: 100%;
    padding: 8px;
    background: #ffffff;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 11px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    /* 隐藏滚动条但保持滚动功能 */
    overflow-y: scroll;
    overflow-x: hidden;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏 Webkit 浏览器的滚动条 */
.log-content::-webkit-scrollbar {
    display: none;
}

/* 日志条目样式 - 支持图标对齐 */
.log-entry {
    margin-bottom: 2px;
    padding: 1px 0;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 11px;
    line-height: 1.4;
    width: 100%;
    text-align: left;
    display: flex;
    align-items: flex-start;
}

.log-entry.info { color: #007bff; }
.log-entry.success { color: #28a745; }
.log-entry.warning { color: #ffc107; }
.log-entry.error { color: #dc3545; }
.log-entry.option { color: #8b5cf6; }
.log-entry.bilibili { color: #ff69b4; }
.log-entry.bing { color: #0078d4; }
.log-entry.signup { color: #6f42c1; }
.log-entry.auto { color: #fd7e14; }
.log-entry.auto-register { color: #f97316; }

/* 表格类型的日志条目 - 保持表格正常显示 */
.log-entry.table {
    margin: 0 !important;
    padding: 0 !important;
    line-height: normal !important;
    display: block;
    font-size: inherit;
    border: none;
    background: transparent;
}

/* 确保日志中的表格有正常的行高和间距 */
.log-entry.table .compact-data-table {
    margin: 0;
    width: 100% !important;
    display: table;
}

.log-entry.table .compact-table-wrapper {
    margin: 0;
    padding: 0;
    width: 100% !important;
    display: block;
}

.log-entry.table .compact-data-table tbody tr {
    height: auto !important;
    min-height: 20px;
}

.log-entry.table .compact-data-table tbody td {
    padding: 2px 4px !important;
    line-height: 1.3 !important;
    vertical-align: middle;
    font-size: 11px !important;
}

.log-entry.table .compact-data-table thead th {
    padding: 3px 4px !important;
    line-height: 1.2 !important;
    font-size: 11px !important;
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    width: 80%;
    height: 80%;
    background: #fff;
    border: 1px solid #dee2e6;
    display: flex;
    flex-direction: column;
}

.modal-header {
    height: 40px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
}

.modal-header h3 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.modal-body {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-spinner {
    background: #fff;
    padding: 24px;
    border-radius: 4px;
    text-align: center;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 14px;
    color: #6c757d;
}



/* 简约表格样式 - 优化左侧日志区域显示 */
.compact-table-wrapper {
    margin: 0;
    padding: 0;
    border: 1px solid #f1f5f9;
    background: #ffffff;
    overflow: hidden;
    display: block;
    line-height: normal;
    font-size: inherit;
    width: 100%;
    max-width: 100%;
}

.compact-data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 10px;
    margin: 0;
    padding: 0;
    background: #ffffff;
    table-layout: fixed;
    line-height: 1.2;
}

/* 简约表头样式 - 适应350px宽度 */
.compact-data-table thead th {
    background: #f8fafc;
    color: #64748b;
    font-weight: 600;
    padding: 4px 6px;
    font-size: 10px;
    text-align: center;
    white-space: nowrap;
    line-height: 1.1;
    border-bottom: 1px solid #f1f5f9;
    letter-spacing: 0.1px;
}

.compact-data-table thead th:first-child {
    text-align: center;
}

.compact-data-table thead th:nth-child(2) {
    text-align: left;
    padding-left: 16px;
}

.compact-data-table thead th:last-child {
    text-align: center;
}

/* 优化列宽设计 - 修复重叠问题 */
.compact-data-table .col-index {
    width: 8%;
    text-align: center;
    white-space: nowrap;
}

.compact-data-table .col-account {
    width: 45%;
    min-width: 100px;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.compact-data-table .col-today,
.compact-data-table .col-yesterday {
    width: 10%;
    text-align: center;
    white-space: nowrap;
}

.compact-data-table .col-status {
    width: 27%;
    text-align: center;
    white-space: nowrap;
}

/* 简约数据行样式 - 适应350px宽度 */
.compact-data-table tbody td {
    padding: 3px 4px;
    border-bottom: 1px solid #f8fafc;
    vertical-align: middle;
    font-size: 10px;
    line-height: 1.1;
    color: #64748b;
    background: #ffffff;
}

/* 序号列样式 */
.compact-data-table tbody td.col-index {
    font-weight: 500;
    color: #94a3b8;
    background: #f8fafc;
}

/* 账号列样式 */
.compact-data-table tbody td.col-account {
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 11px;
    word-break: break-word;
    white-space: normal;
    line-height: 1.3;
    color: #475569;
    max-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 4px 6px;
}

/* 数据列样式 */
.compact-data-table tbody td.col-today,
.compact-data-table tbody td.col-yesterday {
    font-weight: 600;
    font-family: 'Consolas', 'Monaco', monospace;
}

/* 简约行效果 */
.compact-data-table tbody tr:hover {
    background-color: #f9fafb;
}

.compact-data-table tbody tr:hover td {
    background-color: #f9fafb;
}

.compact-data-table tbody tr:hover td.col-index {
    background-color: #f3f4f6;
}

/* 简约签到状态样式 - 仅使用字体颜色 */
.compact-data-table .status-success {
    color: #10b981;
    font-weight: 600;
    font-size: 11px;
    white-space: nowrap;
}

.compact-data-table .status-failed {
    color: #ef4444;
    font-weight: 600;
    font-size: 11px;
    white-space: nowrap;
}

/* 新增状态样式 */
.compact-data-table .status-reward {
    color: #ec4899;
    font-weight: 600;
    font-size: 11px;
    white-space: nowrap;
}

.compact-data-table .status-empty {
    color: #f59e0b;
    font-weight: 600;
    font-size: 11px;
    white-space: nowrap;
}

/* 空白数据样式 */
.compact-data-table .empty-data {
    color: #f59e0b;
    font-weight: 600;
}

/* 保留基本响应式支持 - 但主要针对700px固定宽度设计 */


